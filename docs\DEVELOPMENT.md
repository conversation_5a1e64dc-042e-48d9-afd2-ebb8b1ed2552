# Development Guide

This guide provides detailed information for developers who want to contribute to or modify the Dominations Zygisk Hook module.

## Prerequisites

### Development Environment
- **Operating System**: Linux, macOS, or Windows with WSL
- **Android NDK**: r21e or later
- **Build Tools**: Make, CMake (optional)
- **Text Editor**: Any C++ capable editor (VS Code, CLion, etc.)
- **Version Control**: Git

### Android Development
- **Android SDK**: API 21+ (Android 5.0+)
- **Target Device**: Rooted Android device with Magisk/APatch
- **ADB**: Android Debug Bridge for testing
- **APK Analysis Tools**: jadx, apktool, dex2jar

## Project Structure

```
dominations_zygisk_hook/
├── module.prop              # Module metadata
├── apatch.json             # APatch configuration
├── META-INF/               # Installation scripts
│   └── com/google/android/
│       ├── update-binary   # Magisk installer
│       └── updater-script  # Update script
├── jni/                    # Native C++ code
│   ├── main.cpp           # Zygisk entry point
│   ├── hook.h/.cpp        # Hook management
│   ├── hooks.cpp          # Game-specific hooks
│   ├── logging.h/.cpp     # Logging system
│   ├── zygisk.hpp         # Zygisk API
│   ├── Android.mk         # NDK build
│   └── CMakeLists.txt     # CMake build
├── lib/                   # Compiled libraries
├── service.sh             # Boot service
├── build.sh              # Build script
└── docs/                  # Documentation
```

## Building the Module

### Environment Setup
```bash
# Set NDK path
export ANDROID_NDK_ROOT=/path/to/android-ndk

# Make build script executable
chmod +x build.sh

# Build the module
./build.sh
```

### Build Options
```bash
# Debug build with symbols
./build.sh debug

# Release build (default)
./build.sh release
```

## Code Architecture

### Zygisk Integration
The module uses Zygisk's process injection mechanism:

1. **Module Loading**: Zygisk loads our library into the target process
2. **Process Filtering**: We check if the current process is our target app
3. **Hook Installation**: Install hooks after app specialization
4. **Runtime Monitoring**: Monitor and modify app behavior

### Hook Management System
The `HookManager` class provides:
- JNI method hooking capabilities
- Memory patching utilities
- Symbol resolution
- Pattern matching
- Cleanup management

### Logging System
Comprehensive logging with:
- Multiple log levels (VERBOSE, DEBUG, INFO, WARN, ERROR)
- Hex dump utilities
- Performance timing
- Safe string handling

## Adding New Hooks

### Step 1: Reverse Engineer Target
```bash
# Decompile APK
jadx -d output_dir target.apk

# Analyze with apktool
apktool d target.apk

# Find target methods/classes
grep -r "methodName" output_dir/
```

### Step 2: Define Hook Structure
```cpp
// In hooks.cpp
static jint (*original_targetMethod)(JNIEnv* env, jobject thiz, jint param) = nullptr;

jint hooked_targetMethod(JNIEnv* env, jobject thiz, jint param) {
    LOGD("targetMethod called with param: %d", param);
    
    // Call original
    jint result = original_targetMethod(env, thiz, param);
    
    // Modify behavior
    return result;
}
```

### Step 3: Register Hook
```cpp
void hookNewCategory(JNIEnv* env) {
    auto* hookManager = HookManager::getInstance();
    
    JNIMethodHook hook = {
        "com/example/TargetClass",
        "targetMethod",
        "(I)I",
        reinterpret_cast<void*>(hooked_targetMethod),
        reinterpret_cast<void**>(&original_targetMethod),
        false
    };
    
    hookManager->hookJNIMethod(hook);
}
```

### Step 4: Initialize in Main
```cpp
// In main.cpp initializeHooks()
DominationsHook::Hooks::hookNewCategory(env);
```

## Memory Patching

### Finding Target Addresses
```cpp
// Find symbol in library
void* symbol = hookManager->findSymbol("libtarget.so", "target_function");

// Find pattern in memory
std::vector<uint8_t> pattern = {0x48, 0x89, 0xe5}; // mov rbp, rsp
void* address = hookManager->findPattern(start_addr, size, pattern);
```

### Applying Patches
```cpp
// Patch bytes
std::vector<uint8_t> patch = {0x90, 0x90, 0x90}; // NOP instructions
hookManager->applyMemoryPatch(address, patch);

// Revert patch
hookManager->revertMemoryPatch(address);
```

## Testing and Debugging

### Local Testing
```bash
# Build and install
./build.sh
adb install dominations_zygisk_hook.zip

# Monitor logs
adb logcat | grep DominationsHook

# Check module status
adb shell su -c "ls /data/adb/modules/"
```

### Debugging Techniques
1. **Verbose Logging**: Enable detailed logs
2. **Incremental Hooks**: Test one hook at a time
3. **Memory Dumps**: Analyze memory contents
4. **Process Monitoring**: Track app behavior

### Common Issues
- **Hook Not Triggering**: Check class/method names
- **App Crashes**: Verify hook signatures
- **Module Not Loading**: Check Zygisk status
- **Build Failures**: Verify NDK setup

## Security Considerations

### Anti-Detection
- Minimize memory footprint
- Use indirect function calls
- Randomize hook timing
- Avoid obvious patterns

### Safe Practices
- Always validate parameters
- Handle exceptions gracefully
- Clean up resources properly
- Respect memory boundaries

## Contributing

### Code Style
- Use consistent indentation (4 spaces)
- Follow C++ naming conventions
- Add comprehensive comments
- Include error handling

### Pull Request Process
1. Fork the repository
2. Create feature branch
3. Implement changes
4. Add tests if applicable
5. Update documentation
6. Submit pull request

### Review Criteria
- Code quality and style
- Security considerations
- Performance impact
- Documentation completeness
- Testing coverage

## Advanced Topics

### Custom Hook Libraries
Integration with external hooking frameworks:
- Dobby
- Frida-gum
- Substrate
- Custom implementations

### Anti-Cheat Bypass
Techniques for evading detection:
- Code obfuscation
- Dynamic loading
- Memory protection
- Timing randomization

### Performance Optimization
- Minimize hook overhead
- Efficient memory usage
- Lazy initialization
- Resource pooling

## Resources

### Documentation
- [Zygisk Documentation](https://github.com/topjohnwu/Magisk/blob/master/docs/guides.md#zygisk)
- [Android NDK Guide](https://developer.android.com/ndk/guides)
- [JNI Specification](https://docs.oracle.com/javase/8/docs/technotes/guides/jni/)

### Tools
- [jadx](https://github.com/skylot/jadx) - APK decompiler
- [apktool](https://ibotpeaches.github.io/Apktool/) - APK analysis
- [Frida](https://frida.re/) - Dynamic analysis
- [Ghidra](https://ghidra-sre.org/) - Reverse engineering

### Community
- [XDA Developers](https://forum.xda-developers.com/)
- [Android Reverse Engineering](https://www.reddit.com/r/androiddev/)
- [Magisk Community](https://github.com/topjohnwu/Magisk/discussions)
