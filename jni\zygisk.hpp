// Zygisk API header - simplified version
// For the complete version, download from: https://github.com/topjohnwu/Magisk/blob/master/loader/src/include/zygisk.hpp

#pragma once

#include <jni.h>

#define ZYGISK_API_VERSION 4

namespace zygisk {

struct AppSpecializeArgs {
    jint &uid;
    jint &gid;
    jintArray &gids;
    jint &runtime_flags;
    jobjectArray &rlimits;
    jint &mount_external;
    jstring &se_info;
    jstring &nice_name;
    jstring &instruction_set;
    jstring &app_data_dir;
    jboolean *const is_child_zygote;
    jboolean *const is_top_app;
    jobjectArray *const pkg_data_info_list;
    jobjectArray *const whitelisted_data_info_list;
    jboolean *const mount_data_dirs;
    jboolean *const mount_storage_dirs;

    AppSpecializeArgs(
        jint &uid, jint &gid, jintArray &gids, jint &runtime_flags,
        jobjectArray &rlimits, jint &mount_external, jstring &se_info,
        jstring &nice_name, jstring &instruction_set, jstring &app_data_dir,
        jboolean *is_child_zygote, jboolean *is_top_app,
        jobjectArray *pkg_data_info_list, jobjectArray *whitelisted_data_info_list,
        jboolean *mount_data_dirs, jboolean *mount_storage_dirs) :
        uid(uid), gid(gid), gids(gids), runtime_flags(runtime_flags),
        rlimits(rlimits), mount_external(mount_external), se_info(se_info),
        nice_name(nice_name), instruction_set(instruction_set),
        app_data_dir(app_data_dir), is_child_zygote(is_child_zygote),
        is_top_app(is_top_app), pkg_data_info_list(pkg_data_info_list),
        whitelisted_data_info_list(whitelisted_data_info_list),
        mount_data_dirs(mount_data_dirs), mount_storage_dirs(mount_storage_dirs) {}
};

struct ServerSpecializeArgs {
    jint &uid;
    jint &gid;
    jintArray &gids;
    jint &runtime_flags;
    jobjectArray &rlimits;

    ServerSpecializeArgs(
        jint &uid, jint &gid, jintArray &gids, jint &runtime_flags, jobjectArray &rlimits) :
        uid(uid), gid(gid), gids(gids), runtime_flags(runtime_flags), rlimits(rlimits) {}
};

class ModuleBase {
public:
    virtual void onLoad(Api *api, JNIEnv *env) {}
    virtual void preAppSpecialize(AppSpecializeArgs *args) {}
    virtual void postAppSpecialize(const AppSpecializeArgs *args) {}
    virtual void preServerSpecialize(ServerSpecializeArgs *args) {}
    virtual void postServerSpecialize(const ServerSpecializeArgs *args) {}
};

enum Option : int {
    DLCLOSE_MODULE_LIBRARY = 0,
    FORCE_DENYLIST_UNMOUNT = 1,
};

class Api {
public:
    virtual void setOption(Option opt) = 0;
    virtual int connectCompanion() = 0;
    virtual void setCompanionSocket(int fd) = 0;
    virtual int getModuleDir() = 0;
    virtual uint32_t getFlags() = 0;
    virtual void pltHookRegister(const char *regex, const char *symbol, void *fn, void **backup) = 0;
    virtual void pltHookExclude(const char *regex, const char *symbol) = 0;
    virtual bool pltHookCommit() = 0;
};

} // namespace zygisk

#define REGISTER_ZYGISK_MODULE(clazz) \
extern "C" { \
    void zygisk_module_entry(zygisk::Api *api, JNIEnv *env) { \
        static clazz module; \
        module.onLoad(api, env); \
    } \
    zygisk::ModuleBase *zygisk_module_instance() { \
        static clazz module; \
        return &module; \
    } \
}
