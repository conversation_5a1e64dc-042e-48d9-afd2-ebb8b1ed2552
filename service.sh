#!/system/bin/sh

# Dominations Zygisk Module Service Script
# This script runs during boot to set up the module environment

# Module information
MODULE_ID="dominations_zygisk_hook"
MODULE_PATH="/data/adb/modules/$MODULE_ID"
LOG_FILE="/data/local/tmp/dominations_hook.log"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

log "=== Dominations Zygisk Module Service Started ==="

# Check if module directory exists
if [ ! -d "$MODULE_PATH" ]; then
    log "ERROR: Module directory not found: $MODULE_PATH"
    exit 1
fi

# Check if Z<PERSON>gis<PERSON> is enabled
if [ ! -f "/data/adb/zygisk/zygisk" ]; then
    log "WARNING: Zygisk not detected, module may not work properly"
fi

# Set proper permissions for module files
log "Setting up permissions..."
chmod 755 "$MODULE_PATH"
chmod 644 "$MODULE_PATH/module.prop"
chmod 644 "$MODULE_PATH/apatch.json"

# Set permissions for native libraries
if [ -d "$MODULE_PATH/lib" ]; then
    find "$MODULE_PATH/lib" -name "*.so" -exec chmod 644 {} \;
    log "Native library permissions set"
else
    log "WARNING: No native libraries found"
fi

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"
chmod 666 "$LOG_FILE" 2>/dev/null || true

# Check target application
TARGET_PACKAGE="com.nexonm.dominations.adk"
if pm list packages | grep -q "$TARGET_PACKAGE"; then
    log "Target application found: $TARGET_PACKAGE"
else
    log "WARNING: Target application not installed: $TARGET_PACKAGE"
fi

# Verify Zygisk module is properly loaded
ZYGISK_MODULE_PATH="/data/adb/zygisk"
if [ -d "$ZYGISK_MODULE_PATH" ]; then
    log "Zygisk directory found"
    
    # Check if our module library exists in the right location
    for arch in arm64-v8a armeabi-v7a x86 x86_64; do
        lib_path="$MODULE_PATH/lib/$arch/libdominations_zygisk.so"
        if [ -f "$lib_path" ]; then
            log "Module library found for $arch"
        fi
    done
else
    log "WARNING: Zygisk directory not found"
fi

# Set up environment for debugging (optional)
# Uncomment the following lines to enable verbose logging
# setprop log.tag.DominationsHook VERBOSE
# setprop log.tag.Zygisk VERBOSE

log "=== Dominations Zygisk Module Service Completed ==="

# Exit successfully
exit 0
