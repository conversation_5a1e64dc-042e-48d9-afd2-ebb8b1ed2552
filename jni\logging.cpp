#include "logging.h"
#include <chrono>
#include <iomanip>
#include <sstream>

// Hex dump implementation
void log_hex_dump(const char* prefix, const void* data, size_t size) {
    if (!data || size == 0) {
        LOGD("%s: (empty)", prefix);
        return;
    }
    
    const uint8_t* bytes = static_cast<const uint8_t*>(data);
    std::stringstream ss;
    
    for (size_t i = 0; i < size; ++i) {
        if (i % 16 == 0) {
            if (i > 0) {
                LOGD("%s: %s", prefix, ss.str().c_str());
                ss.str("");
                ss.clear();
            }
            ss << std::hex << std::setfill('0') << std::setw(8) << i << ": ";
        }
        
        ss << std::hex << std::setfill('0') << std::setw(2) << static_cast<int>(bytes[i]) << " ";
        
        if (i % 16 == 15 || i == size - 1) {
            // Pad the line if it's the last incomplete line
            if (i % 16 != 15) {
                for (size_t j = i % 16; j < 15; ++j) {
                    ss << "   ";
                }
            }
            
            ss << " |";
            size_t start = i - (i % 16);
            for (size_t j = start; j <= i; ++j) {
                char c = static_cast<char>(bytes[j]);
                ss << (isprint(c) ? c : '.');
            }
            ss << "|";
            
            LOGD("%s: %s", prefix, ss.str().c_str());
            ss.str("");
            ss.clear();
        }
    }
}

// Timer implementation
Timer::Timer(const std::string& timer_name) : name(timer_name) {
    reset();
}

Timer::~Timer() {
    long long elapsed = elapsed_ms();
    LOGD("Timer [%s]: %lld ms", name.c_str(), elapsed);
}

void Timer::reset() {
    auto now = std::chrono::high_resolution_clock::now();
    start_time = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
}

long long Timer::elapsed_ms() {
    auto now = std::chrono::high_resolution_clock::now();
    long long current_time = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    return current_time - start_time;
}
