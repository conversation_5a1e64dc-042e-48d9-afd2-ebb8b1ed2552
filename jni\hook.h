#ifndef DOMINATIONS_HOOK_H
#define DOMINATIONS_HOOK_H

#include <jni.h>
#include <string>
#include <vector>
#include <functional>

// Hook utilities and definitions
namespace DominationsHook {

    // JNI method hook structure
    struct JNIMethodHook {
        const char* class_name;
        const char* method_name;
        const char* method_signature;
        void* new_method;
        void** original_method;
        bool is_static;
    };

    // Memory patch structure
    struct MemoryPatch {
        void* address;
        std::vector<uint8_t> original_bytes;
        std::vector<uint8_t> patch_bytes;
        bool is_applied;
    };

    // Hook manager class
    class HookManager {
    private:
        static HookManager* instance;
        std::vector<JNIMethodHook> jni_hooks;
        std::vector<MemoryPatch> memory_patches;
        
    public:
        static HookManager* getInstance();
        
        // JNI method hooking
        bool hookJNIMethod(const JNIMethodHook& hook);
        bool unhookJNIMethod(const JNIMethodHook& hook);
        
        // Memory patching
        bool applyMemoryPatch(void* address, const std::vector<uint8_t>& patch_bytes);
        bool revertMemoryPatch(void* address);
        
        // Utility functions
        void* findSymbol(const char* lib_name, const char* symbol_name);
        void* findPattern(void* start, size_t size, const std::vector<uint8_t>& pattern);
        
        // Cleanup
        void cleanup();
    };

    // Common hook implementations
    namespace Hooks {
        // Example hooks for Dominations
        void hookGameLogic(JNIEnv* env);
        void hookNetworking(JNIEnv* env);
        void hookResourceManagement(JNIEnv* env);
        void hookAntiCheat(JNIEnv* env);
    }

    // Utility functions
    bool isTargetProcess(const char* package_name);
    std::string getProcessName();
    bool hasRootAccess();
    void protectMemory(void* addr, size_t size, int prot);
}

#endif // DOMINATIONS_HOOK_H
