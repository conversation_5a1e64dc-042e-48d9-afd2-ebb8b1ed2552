#!/bin/bash

# Dominations Zygisk Module Installation Helper Script
# This script helps with manual installation and setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

MODULE_NAME="dominations_zygisk_hook"
TARGET_PACKAGE="com.nexonm.dominations.adk"

echo -e "${BLUE}=== Dominations Zygisk Module Installation Helper ===${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}Error: This script must be run as root${NC}"
    echo -e "${YELLOW}Please run: sudo $0${NC}"
    exit 1
fi

# Check if ADB is available
if ! command -v adb &> /dev/null; then
    echo -e "${YELLOW}Warning: ADB not found. Some checks will be skipped.${NC}"
    ADB_AVAILABLE=false
else
    ADB_AVAILABLE=true
fi

# Function to check device connection
check_device() {
    if [ "$ADB_AVAILABLE" = true ]; then
        if adb devices | grep -q "device$"; then
            echo -e "${GREEN}✓ Android device connected${NC}"
            return 0
        else
            echo -e "${RED}✗ No Android device connected${NC}"
            return 1
        fi
    fi
    return 0
}

# Function to check if Magisk/APatch is installed
check_root_manager() {
    if [ "$ADB_AVAILABLE" = true ]; then
        if adb shell "su -c 'which magisk'" &>/dev/null; then
            echo -e "${GREEN}✓ Magisk detected${NC}"
            ROOT_MANAGER="magisk"
        elif adb shell "su -c 'which apatch'" &>/dev/null; then
            echo -e "${GREEN}✓ APatch detected${NC}"
            ROOT_MANAGER="apatch"
        else
            echo -e "${YELLOW}⚠ Root manager not detected or not accessible${NC}"
            ROOT_MANAGER="unknown"
        fi
    else
        echo -e "${YELLOW}⚠ Cannot check root manager (ADB not available)${NC}"
        ROOT_MANAGER="unknown"
    fi
}

# Function to check if Zygisk is enabled
check_zygisk() {
    if [ "$ADB_AVAILABLE" = true ] && [ "$ROOT_MANAGER" != "unknown" ]; then
        if adb shell "su -c 'ls /data/adb/zygisk/'" &>/dev/null; then
            echo -e "${GREEN}✓ Zygisk is enabled${NC}"
        else
            echo -e "${RED}✗ Zygisk is not enabled${NC}"
            echo -e "${YELLOW}  Please enable Zygisk in Magisk/APatch settings${NC}"
        fi
    fi
}

# Function to check if target app is installed
check_target_app() {
    if [ "$ADB_AVAILABLE" = true ]; then
        if adb shell "pm list packages | grep $TARGET_PACKAGE" &>/dev/null; then
            echo -e "${GREEN}✓ Target app ($TARGET_PACKAGE) is installed${NC}"
        else
            echo -e "${YELLOW}⚠ Target app ($TARGET_PACKAGE) is not installed${NC}"
            echo -e "${YELLOW}  The module will still work once the app is installed${NC}"
        fi
    fi
}

# Function to install module
install_module() {
    local zip_file="${MODULE_NAME}.zip"
    
    if [ ! -f "$zip_file" ]; then
        echo -e "${RED}Error: Module ZIP file not found: $zip_file${NC}"
        echo -e "${YELLOW}Please build the module first using: ./build.sh${NC}"
        return 1
    fi
    
    if [ "$ADB_AVAILABLE" = true ]; then
        echo -e "${BLUE}Installing module via ADB...${NC}"
        
        # Push module to device
        adb push "$zip_file" "/sdcard/$zip_file"
        
        # Install via Magisk/APatch
        if [ "$ROOT_MANAGER" = "magisk" ]; then
            adb shell "su -c 'magisk --install-module /sdcard/$zip_file'"
        elif [ "$ROOT_MANAGER" = "apatch" ]; then
            adb shell "su -c 'apatch module install /sdcard/$zip_file'"
        else
            echo -e "${YELLOW}Please install manually via Magisk Manager or APatch Manager${NC}"
            echo -e "${YELLOW}Module file pushed to: /sdcard/$zip_file${NC}"
        fi
        
        # Clean up
        adb shell "rm -f /sdcard/$zip_file"
        
        echo -e "${GREEN}✓ Module installation completed${NC}"
        echo -e "${YELLOW}Please reboot your device to activate the module${NC}"
    else
        echo -e "${YELLOW}ADB not available. Please install manually:${NC}"
        echo -e "${YELLOW}1. Copy $zip_file to your device${NC}"
        echo -e "${YELLOW}2. Install via Magisk Manager or APatch Manager${NC}"
        echo -e "${YELLOW}3. Reboot your device${NC}"
    fi
}

# Main installation process
echo -e "${BLUE}Performing pre-installation checks...${NC}"

check_device
check_root_manager
check_zygisk
check_target_app

echo -e "\n${BLUE}Installation Summary:${NC}"
echo -e "Module: $MODULE_NAME"
echo -e "Target: $TARGET_PACKAGE"
echo -e "Root Manager: $ROOT_MANAGER"

echo -e "\n${YELLOW}Do you want to proceed with installation? (y/N)${NC}"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    install_module
    echo -e "\n${GREEN}=== Installation completed! ===${NC}"
    echo -e "${YELLOW}Remember to reboot your device and launch the target app${NC}"
else
    echo -e "${YELLOW}Installation cancelled${NC}"
fi
