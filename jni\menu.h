#ifndef DOMINATIONS_MENU_H
#define DOMINATIONS_MENU_H

#include <jni.h>
#include <string>
#include <vector>
#include <functional>

namespace DominationsHook {
namespace Menu {

    // Menu item types
    enum class MenuItemType {
        TOGGLE,
        SLIDER,
        BUTTON,
        SEPARATOR,
        CATEGORY
    };

    // Menu item structure
    struct MenuItem {
        std::string id;
        std::string title;
        std::string description;
        MenuItemType type;
        bool enabled;
        int value;
        int min_value;
        int max_value;
        std::function<void(bool)> toggle_callback;
        std::function<void(int)> slider_callback;
        std::function<void()> button_callback;
    };

    // Menu categories
    struct MenuCategory {
        std::string title;
        std::vector<MenuItem> items;
        bool expanded;
    };

    // Main menu class
    class ModMenu {
    private:
        static ModMenu* instance;
        JNIEnv* env;
        jobject activity;
        bool menu_visible;
        bool menu_initialized;
        std::vector<MenuCategory> categories;
        
        // UI elements
        jobject menu_dialog;
        jobject menu_layout;
        
        // Feature states
        struct FeatureStates {
            bool unlimited_resources;
            bool instant_build;
            bool no_troop_cost;
            bool speed_hack;
            bool god_mode;
            bool auto_collect;
            bool anti_ban;
            int speed_multiplier;
            int resource_multiplier;
        } features;

    public:
        static ModMenu* getInstance();
        
        // Initialization
        bool initialize(JNIEnv* env, jobject activity);
        void cleanup();
        
        // Menu management
        void showMenu();
        void hideMenu();
        void toggleMenu();
        bool isMenuVisible() const { return menu_visible; }
        
        // Menu building
        void buildMenu();
        void addCategory(const std::string& title);
        void addToggleItem(const std::string& category, const std::string& id, 
                          const std::string& title, const std::string& description,
                          bool default_value, std::function<void(bool)> callback);
        void addSliderItem(const std::string& category, const std::string& id,
                          const std::string& title, const std::string& description,
                          int default_value, int min_val, int max_val,
                          std::function<void(int)> callback);
        void addButtonItem(const std::string& category, const std::string& id,
                          const std::string& title, const std::string& description,
                          std::function<void()> callback);
        
        // Feature getters
        bool isUnlimitedResourcesEnabled() const { return features.unlimited_resources; }
        bool isInstantBuildEnabled() const { return features.instant_build; }
        bool isNoTroopCostEnabled() const { return features.no_troop_cost; }
        bool isSpeedHackEnabled() const { return features.speed_hack; }
        bool isGodModeEnabled() const { return features.god_mode; }
        bool isAutoCollectEnabled() const { return features.auto_collect; }
        bool isAntiBanEnabled() const { return features.anti_ban; }
        int getSpeedMultiplier() const { return features.speed_multiplier; }
        int getResourceMultiplier() const { return features.resource_multiplier; }
        
        // Touch handling
        void handleTouch(float x, float y);
        
    private:
        // UI creation helpers
        jobject createMenuDialog();
        jobject createMenuLayout();
        void createMenuItems();
        
        // Feature callbacks
        void onUnlimitedResourcesToggle(bool enabled);
        void onInstantBuildToggle(bool enabled);
        void onNoTroopCostToggle(bool enabled);
        void onSpeedHackToggle(bool enabled);
        void onGodModeToggle(bool enabled);
        void onAutoCollectToggle(bool enabled);
        void onAntiBanToggle(bool enabled);
        void onSpeedMultiplierChange(int value);
        void onResourceMultiplierChange(int value);
        void onSaveSettings();
        void onLoadSettings();
        void onResetSettings();
        
        // Settings persistence
        void saveSettings();
        void loadSettings();
    };

    // Touch overlay for menu activation
    class TouchOverlay {
    private:
        static TouchOverlay* instance;
        bool overlay_active;
        float menu_button_x, menu_button_y;
        float menu_button_size;
        
    public:
        static TouchOverlay* getInstance();
        
        void initialize();
        void cleanup();
        bool handleTouch(float x, float y);
        void draw();
        
    private:
        bool isPointInMenuButton(float x, float y);
    };

} // namespace Menu
} // namespace DominationsHook

#endif // DOMINATIONS_MENU_H
