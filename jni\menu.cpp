#include "menu.h"
#include "logging.h"
#include <android/native_window.h>
#include <android/native_window_jni.h>

namespace DominationsHook {
namespace Menu {

// Static instances
ModMenu* ModMenu::instance = nullptr;
TouchOverlay* TouchOverlay::instance = nullptr;

ModMenu* ModMenu::getInstance() {
    if (!instance) {
        instance = new ModMenu();
    }
    return instance;
}

bool ModMenu::initialize(JNIEnv* env, jobject activity) {
    LOG_ENTRY();
    
    this->env = env;
    this->activity = env->NewGlobalRef(activity);
    
    // Initialize feature states
    features = {
        .unlimited_resources = false,
        .instant_build = false,
        .no_troop_cost = false,
        .speed_hack = false,
        .god_mode = false,
        .auto_collect = false,
        .anti_ban = true, // Default enabled for safety
        .speed_multiplier = 1,
        .resource_multiplier = 1
    };
    
    // Load saved settings
    loadSettings();
    
    // Build menu structure
    buildMenu();
    
    menu_initialized = true;
    menu_visible = false;
    
    LOGI("Mod menu initialized successfully");
    return true;
}

void ModMenu::buildMenu() {
    LOG_ENTRY();
    
    categories.clear();
    
    // Resource Hacks Category
    addCategory("Resource Hacks");
    addToggleItem("Resource Hacks", "unlimited_resources", 
                  "Unlimited Resources", "Infinite gold, gems, and other resources",
                  features.unlimited_resources,
                  [this](bool enabled) { onUnlimitedResourcesToggle(enabled); });
    
    addSliderItem("Resource Hacks", "resource_multiplier",
                  "Resource Multiplier", "Multiply resource gains",
                  features.resource_multiplier, 1, 100,
                  [this](int value) { onResourceMultiplierChange(value); });
    
    addToggleItem("Resource Hacks", "auto_collect",
                  "Auto Collect", "Automatically collect resources",
                  features.auto_collect,
                  [this](bool enabled) { onAutoCollectToggle(enabled); });
    
    // Building Hacks Category
    addCategory("Building Hacks");
    addToggleItem("Building Hacks", "instant_build",
                  "Instant Build", "Buildings complete instantly",
                  features.instant_build,
                  [this](bool enabled) { onInstantBuildToggle(enabled); });
    
    // Battle Hacks Category
    addCategory("Battle Hacks");
    addToggleItem("Battle Hacks", "no_troop_cost",
                  "No Troop Cost", "Train troops for free",
                  features.no_troop_cost,
                  [this](bool enabled) { onNoTroopCostToggle(enabled); });
    
    addToggleItem("Battle Hacks", "god_mode",
                  "God Mode", "Your troops take no damage",
                  features.god_mode,
                  [this](bool enabled) { onGodModeToggle(enabled); });
    
    // Speed Hacks Category
    addCategory("Speed Hacks");
    addToggleItem("Speed Hacks", "speed_hack",
                  "Speed Hack", "Accelerate game speed",
                  features.speed_hack,
                  [this](bool enabled) { onSpeedHackToggle(enabled); });
    
    addSliderItem("Speed Hacks", "speed_multiplier",
                  "Speed Multiplier", "Game speed multiplier",
                  features.speed_multiplier, 1, 10,
                  [this](int value) { onSpeedMultiplierChange(value); });
    
    // Safety Category
    addCategory("Safety");
    addToggleItem("Safety", "anti_ban",
                  "Anti-Ban Protection", "Enable safety measures",
                  features.anti_ban,
                  [this](bool enabled) { onAntiBanToggle(enabled); });
    
    // Settings Category
    addCategory("Settings");
    addButtonItem("Settings", "save_settings",
                  "Save Settings", "Save current configuration",
                  [this]() { onSaveSettings(); });
    
    addButtonItem("Settings", "load_settings",
                  "Load Settings", "Load saved configuration",
                  [this]() { onLoadSettings(); });
    
    addButtonItem("Settings", "reset_settings",
                  "Reset Settings", "Reset to default values",
                  [this]() { onResetSettings(); });
    
    LOGI("Menu structure built with %zu categories", categories.size());
}

void ModMenu::addCategory(const std::string& title) {
    MenuCategory category;
    category.title = title;
    category.expanded = true;
    categories.push_back(category);
}

void ModMenu::addToggleItem(const std::string& category, const std::string& id,
                           const std::string& title, const std::string& description,
                           bool default_value, std::function<void(bool)> callback) {
    MenuItem item;
    item.id = id;
    item.title = title;
    item.description = description;
    item.type = MenuItemType::TOGGLE;
    item.enabled = default_value;
    item.toggle_callback = callback;
    
    // Find category and add item
    for (auto& cat : categories) {
        if (cat.title == category) {
            cat.items.push_back(item);
            break;
        }
    }
}

void ModMenu::addSliderItem(const std::string& category, const std::string& id,
                           const std::string& title, const std::string& description,
                           int default_value, int min_val, int max_val,
                           std::function<void(int)> callback) {
    MenuItem item;
    item.id = id;
    item.title = title;
    item.description = description;
    item.type = MenuItemType::SLIDER;
    item.value = default_value;
    item.min_value = min_val;
    item.max_value = max_val;
    item.slider_callback = callback;
    
    // Find category and add item
    for (auto& cat : categories) {
        if (cat.title == category) {
            cat.items.push_back(item);
            break;
        }
    }
}

void ModMenu::addButtonItem(const std::string& category, const std::string& id,
                           const std::string& title, const std::string& description,
                           std::function<void()> callback) {
    MenuItem item;
    item.id = id;
    item.title = title;
    item.description = description;
    item.type = MenuItemType::BUTTON;
    item.button_callback = callback;
    
    // Find category and add item
    for (auto& cat : categories) {
        if (cat.title == category) {
            cat.items.push_back(item);
            break;
        }
    }
}

void ModMenu::showMenu() {
    if (!menu_initialized) {
        LOGE("Menu not initialized");
        return;
    }
    
    menu_visible = true;
    LOGI("Mod menu shown");
    
    // In a real implementation, this would create and show the actual UI
    // For now, we'll log the menu structure
    LOGI("=== DOMINATIONS MOD MENU ===");
    for (const auto& category : categories) {
        LOGI("[%s]", category.title.c_str());
        for (const auto& item : category.items) {
            switch (item.type) {
                case MenuItemType::TOGGLE:
                    LOGI("  %s: %s", item.title.c_str(), item.enabled ? "ON" : "OFF");
                    break;
                case MenuItemType::SLIDER:
                    LOGI("  %s: %d", item.title.c_str(), item.value);
                    break;
                case MenuItemType::BUTTON:
                    LOGI("  [%s]", item.title.c_str());
                    break;
                default:
                    break;
            }
        }
    }
    LOGI("============================");
}

void ModMenu::hideMenu() {
    menu_visible = false;
    LOGI("Mod menu hidden");
}

void ModMenu::toggleMenu() {
    if (menu_visible) {
        hideMenu();
    } else {
        showMenu();
    }
}

// Feature callback implementations
void ModMenu::onUnlimitedResourcesToggle(bool enabled) {
    features.unlimited_resources = enabled;
    LOGI("Unlimited Resources: %s", enabled ? "ENABLED" : "DISABLED");
}

void ModMenu::onInstantBuildToggle(bool enabled) {
    features.instant_build = enabled;
    LOGI("Instant Build: %s", enabled ? "ENABLED" : "DISABLED");
}

void ModMenu::onNoTroopCostToggle(bool enabled) {
    features.no_troop_cost = enabled;
    LOGI("No Troop Cost: %s", enabled ? "ENABLED" : "DISABLED");
}

void ModMenu::onSpeedHackToggle(bool enabled) {
    features.speed_hack = enabled;
    LOGI("Speed Hack: %s", enabled ? "ENABLED" : "DISABLED");
}

void ModMenu::onGodModeToggle(bool enabled) {
    features.god_mode = enabled;
    LOGI("God Mode: %s", enabled ? "ENABLED" : "DISABLED");
}

void ModMenu::onAutoCollectToggle(bool enabled) {
    features.auto_collect = enabled;
    LOGI("Auto Collect: %s", enabled ? "ENABLED" : "DISABLED");
}

void ModMenu::onAntiBanToggle(bool enabled) {
    features.anti_ban = enabled;
    LOGI("Anti-Ban Protection: %s", enabled ? "ENABLED" : "DISABLED");
}

void ModMenu::onSpeedMultiplierChange(int value) {
    features.speed_multiplier = value;
    LOGI("Speed Multiplier: %dx", value);
}

void ModMenu::onResourceMultiplierChange(int value) {
    features.resource_multiplier = value;
    LOGI("Resource Multiplier: %dx", value);
}

void ModMenu::onSaveSettings() {
    saveSettings();
    LOGI("Settings saved");
}

void ModMenu::onLoadSettings() {
    loadSettings();
    LOGI("Settings loaded");
}

void ModMenu::onResetSettings() {
    // Reset to defaults
    features = {
        .unlimited_resources = false,
        .instant_build = false,
        .no_troop_cost = false,
        .speed_hack = false,
        .god_mode = false,
        .auto_collect = false,
        .anti_ban = true,
        .speed_multiplier = 1,
        .resource_multiplier = 1
    };
    LOGI("Settings reset to defaults");
}

void ModMenu::saveSettings() {
    // In a real implementation, save to file or shared preferences
    LOGD("Saving settings to persistent storage");
}

void ModMenu::loadSettings() {
    // In a real implementation, load from file or shared preferences
    LOGD("Loading settings from persistent storage");
}

void ModMenu::cleanup() {
    if (activity) {
        env->DeleteGlobalRef(activity);
        activity = nullptr;
    }
    menu_initialized = false;
    menu_visible = false;
}

// TouchOverlay implementation
TouchOverlay* TouchOverlay::getInstance() {
    if (!instance) {
        instance = new TouchOverlay();
    }
    return instance;
}

void TouchOverlay::initialize() {
    overlay_active = true;
    menu_button_x = 50.0f;  // Top-left corner
    menu_button_y = 50.0f;
    menu_button_size = 80.0f;
    LOGI("Touch overlay initialized");
}

bool TouchOverlay::handleTouch(float x, float y) {
    if (!overlay_active) return false;
    
    if (isPointInMenuButton(x, y)) {
        LOGI("Menu button touched at (%.2f, %.2f)", x, y);
        ModMenu::getInstance()->toggleMenu();
        return true;
    }
    
    return false;
}

bool TouchOverlay::isPointInMenuButton(float x, float y) {
    return (x >= menu_button_x && x <= menu_button_x + menu_button_size &&
            y >= menu_button_y && y <= menu_button_y + menu_button_size);
}

void TouchOverlay::cleanup() {
    overlay_active = false;
}

} // namespace Menu
} // namespace DominationsHook
