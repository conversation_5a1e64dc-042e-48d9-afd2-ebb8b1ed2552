LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)

LOCAL_MODULE := dominations_zygisk
LOCAL_SRC_FILES := main.cpp hook.cpp hooks.cpp logging.cpp menu.cpp
LOCAL_STATIC_LIBRARIES := 
LOCAL_SHARED_LIBRARIES := 

LOCAL_LDLIBS := -llog -ldl
LOCAL_CPPFLAGS := -std=c++17 -fvisibility=hidden -fvisibility-inlines-hidden
LOCAL_CFLAGS := -Wno-error=format-security -ffunction-sections -fdata-sections
LOCAL_LDFLAGS := -Wl,--gc-sections,--strip-all

# Enable optimization
LOCAL_CPPFLAGS += -O2 -DNDEBUG

# Include Zygisk headers (you need to download zygisk.hpp)
LOCAL_C_INCLUDES := $(LOCAL_PATH)

include $(BUILD_SHARED_LIBRARY)
