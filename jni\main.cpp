#include <jni.h>
#include <unistd.h>
#include <string>
#include <vector>
#include <dlfcn.h>
#include <sys/mman.h>
#include <chrono>

#include "zygisk.hpp"
#include "logging.h"
#include "hook.h"
#include "menu.h"

using zygisk::Api;
using zygisk::AppSpecializeArgs;
using zygisk::ServerSpecializeArgs;

// Target package name
static const char* TARGET_PACKAGE = "com.nexonm.dominations.adk";

class DominationsModule : public zygisk::ModuleBase {
private:
    Api* api;
    JNIEnv* env;
    bool is_target_app = false;

public:
    void onLoad(Api* api, JNIEnv* env) override {
        this->api = api;
        this->env = env;
        LOGI("Dominations Zygisk module loaded");
    }

    void preAppSpecialize(AppSpecializeArgs* args) override {
        // Check if this is our target application
        const char* package_name = env->GetStringUTFChars(args->nice_name, nullptr);
        
        if (package_name) {
            LOGD("Process: %s", package_name);
            
            if (strcmp(package_name, TARGET_PACKAGE) == 0) {
                is_target_app = true;
                LOGI("Target app detected: %s", package_name);
                
                // Keep our module loaded for the target app
                api->setOption(zygisk::DLCLOSE_MODULE_LIBRARY);
            }
            
            env->ReleaseStringUTFChars(args->nice_name, package_name);
        }
    }

    void postAppSpecialize(const AppSpecializeArgs* args) override {
        if (!is_target_app) {
            return;
        }

        LOGI("Post app specialize for target app");
        
        // Initialize hooks after app specialization
        initializeHooks();
    }

    void preServerSpecialize(ServerSpecializeArgs* args) override {
        // Not needed for app-specific hooks
        api->setOption(zygisk::DLCLOSE_MODULE_LIBRARY);
    }

private:
    void initializeHooks() {
        LOGI("Initializing hooks for Dominations");

        try {
            // Get HookManager instance
            auto* hookManager = DominationsHook::HookManager::getInstance();

            // Initialize menu system first
            initializeMenu();

            // Initialize common hooks
            DominationsHook::Hooks::hookGameLogic(env);
            DominationsHook::Hooks::hookNetworking(env);
            DominationsHook::Hooks::hookResourceManagement(env);
            DominationsHook::Hooks::hookAntiCheat(env);

            LOGI("Hooks initialized successfully");

        } catch (const std::exception& e) {
            LOGE("Failed to initialize hooks: %s", e.what());
        } catch (...) {
            LOGE("Unknown error occurred during hook initialization");
        }
    }

    void initializeMenu() {
        LOGI("Initializing mod menu system");

        try {
            // Get activity context (this would need proper implementation)
            jobject activity = getActivityContext();

            // Initialize menu
            auto* menu = DominationsHook::Menu::ModMenu::getInstance();
            if (menu->initialize(env, activity)) {
                LOGI("Mod menu initialized successfully");

                // Initialize touch overlay
                auto* overlay = DominationsHook::Menu::TouchOverlay::getInstance();
                overlay->initialize();

                // Show welcome message
                LOGI("=== DOMINATIONS MOD MENU ACTIVATED ===");
                LOGI("Touch the top-left corner to open the menu");
                LOGI("=====================================");

            } else {
                LOGE("Failed to initialize mod menu");
            }

        } catch (const std::exception& e) {
            LOGE("Failed to initialize menu: %s", e.what());
        }
    }

    jobject getActivityContext() {
        // This is a placeholder - in a real implementation, you would need to
        // find the current activity context through JNI calls
        // For now, return nullptr and handle gracefully
        LOGW("Activity context not implemented - menu will use fallback mode");
        return nullptr;
    }
};

// Zygisk module registration
REGISTER_ZYGISK_MODULE(DominationsModule)

// Companion process entry point (if needed)
static void companion_handler(int i) {
    LOGI("Companion process started");
    // Implement companion process logic here if needed
}

// Module initialization for APatch compatibility
extern "C" {
    // APatch module entry point
    __attribute__((constructor))
    void module_init() {
        LOGI("Module constructor called");
    }
    
    __attribute__((destructor))
    void module_cleanup() {
        LOGI("Module destructor called");
        auto* hookManager = DominationsHook::HookManager::getInstance();
        if (hookManager) {
            hookManager->cleanup();
        }
    }
}
