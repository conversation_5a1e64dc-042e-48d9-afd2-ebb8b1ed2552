MIT License

Copyright (c) 2024 Dominations Zygisk Hook Module

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

ADDITIONAL DISCLAIMERS:

1. EDUCATIONAL PURPOSE: This software is intended for educational and research 
   purposes only. Users are responsible for ensuring their use complies with 
   applicable laws and terms of service.

2. GAME MODIFICATION: Modifying games may violate the game's Terms of Service 
   and could result in account suspension or termination. Use at your own risk.

3. REVERSE ENGINEERING: This software is designed for legitimate reverse 
   engineering research. Users must respect intellectual property rights and 
   applicable laws in their jurisdiction.

4. NO WARRANTY: The authors provide no warranty that this software will work 
   with any particular version of the target application or that it will 
   continue to work as applications are updated.

5. SECURITY: Users are responsible for understanding the security implications 
   of running this software on their devices.

By using this software, you acknowledge that you have read and understood 
these terms and agree to use the software responsibly and legally.
