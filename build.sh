#!/bin/bash

# Dominations Zygisk Module Build Script
# This script builds the native library for the Zygisk module

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MODULE_NAME="dominations_zygisk_hook"
NDK_PATH="${ANDROID_NDK_ROOT:-$ANDROID_NDK_HOME}"
BUILD_TYPE="${1:-release}"

echo -e "${BLUE}=== Dominations Zygisk Module Build Script ===${NC}"
echo -e "${BLUE}Build type: ${BUILD_TYPE}${NC}"

# Check if NDK is available
if [ -z "$NDK_PATH" ]; then
    echo -e "${RED}Error: Android NDK not found!${NC}"
    echo -e "${YELLOW}Please set ANDROID_NDK_ROOT or ANDROID_NDK_HOME environment variable${NC}"
    exit 1
fi

if [ ! -d "$NDK_PATH" ]; then
    echo -e "${RED}Error: NDK path does not exist: $NDK_PATH${NC}"
    exit 1
fi

echo -e "${GREEN}Using NDK: $NDK_PATH${NC}"

# Clean previous builds
echo -e "${BLUE}Cleaning previous builds...${NC}"
rm -rf libs obj

# Create lib directories
mkdir -p lib/arm64-v8a
mkdir -p lib/armeabi-v7a
mkdir -p lib/x86
mkdir -p lib/x86_64

# Build using ndk-build
echo -e "${BLUE}Building native library...${NC}"
cd jni

if [ "$BUILD_TYPE" = "debug" ]; then
    "$NDK_PATH/ndk-build" NDK_DEBUG=1 V=1
else
    "$NDK_PATH/ndk-build" NDK_DEBUG=0
fi

cd ..

# Copy libraries to module structure
echo -e "${BLUE}Copying libraries...${NC}"
if [ -d "libs" ]; then
    cp -r libs/* lib/
    rm -rf libs obj
fi

# Verify build
echo -e "${BLUE}Verifying build...${NC}"
for arch in arm64-v8a armeabi-v7a x86 x86_64; do
    lib_file="lib/$arch/libdominations_zygisk.so"
    if [ -f "$lib_file" ]; then
        echo -e "${GREEN}✓ Built for $arch${NC}"
        file_size=$(stat -c%s "$lib_file" 2>/dev/null || stat -f%z "$lib_file" 2>/dev/null || echo "unknown")
        echo -e "  Size: $file_size bytes"
    else
        echo -e "${YELLOW}⚠ Missing build for $arch${NC}"
    fi
done

# Create module package
echo -e "${BLUE}Creating module package...${NC}"
zip -r "${MODULE_NAME}.zip" \
    module.prop \
    META-INF/ \
    lib/ \
    service.sh \
    apatch.json \
    -x "*.git*" "*.DS_Store" "build.sh" "jni/" "*.md"

echo -e "${GREEN}=== Build completed successfully! ===${NC}"
echo -e "${GREEN}Module package: ${MODULE_NAME}.zip${NC}"
echo -e "${YELLOW}Install via Magisk Manager or APatch Manager${NC}"
