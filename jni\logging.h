#ifndef DOMINATIONS_LOGGING_H
#define DOMINATIONS_LOGGING_H

#include <android/log.h>
#include <string>

#define LOG_TAG "DominationsHook"

// Log levels
#define LOGV(...) __android_log_print(ANDROID_LOG_VERBOSE, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGF(...) __android_log_print(ANDROID_LOG_FATAL, LOG_TAG, __VA_ARGS__)

// Utility macros for common logging patterns
#define LOG_ENTRY() LOGD("Entering %s", __FUNCTION__)
#define LOG_EXIT() LOGD("Exiting %s", __FUNCTION__)
#define LOG_ERROR_AND_RETURN(ret) do { LOGE("Error in %s", __FUNCTION__); return ret; } while(0)

// Safe string logging
inline void log_safe_string(const char* prefix, const char* str) {
    if (str) {
        LOGD("%s: %s", prefix, str);
    } else {
        LOGD("%s: (null)", prefix);
    }
}

// Hex dump utility for debugging
void log_hex_dump(const char* prefix, const void* data, size_t size);

// Performance timing utilities
class Timer {
private:
    std::string name;
    long long start_time;
    
public:
    Timer(const std::string& timer_name);
    ~Timer();
    void reset();
    long long elapsed_ms();
};

#endif // DOMINATIONS_LOGGING_H
