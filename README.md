# Dominations Zygisk Hook Module

A comprehensive Magisk/APatch module that uses <PERSON><PERSON><PERSON><PERSON> to hook into the Dominations game (`com.nexonm.dominations.adk`) for reverse engineering and modding purposes.

## Features

- **🎮 Interactive Mod Menu**: Touch-activated menu system for real-time control
- **🔧 Zygisk Integration**: Uses Zygisk's powerful hooking capabilities
- **🔄 Dual Compatibility**: Works with both Magisk and APatch frameworks
- **⚡ Real-time Modifications**: Enable/disable features while playing
- **💰 Resource Hacks**: Unlimited gold, gems, and resource multipliers
- **🏗️ Building Hacks**: Instant construction and speed modifications
- **⚔️ Battle Hacks**: God mode, free troops, and combat advantages
- **🚀 Speed Control**: Adjustable game speed multipliers
- **🛡️ Safety Features**: Anti-ban protection and detection avoidance
- **📊 Network Monitoring**: Log and analyze network requests
- **🔍 Anti-Cheat Analysis**: Study anti-cheat mechanisms
- **📝 Comprehensive Logging**: Detailed logging for debugging and analysis
- **🏗️ Multi-Architecture**: Supports ARM64, ARM32, x86, and x86_64

## Requirements

- **Root Access**: Magisk v20.4+ or APatch v10568+
- **Zygisk Enabled**: Must be enabled in Magisk/APatch settings
- **Target App**: Dominations game installed (`com.nexonm.dominations.adk`)
- **Android Version**: Android 7.0+ (API 24+)

## Installation

### Method 1: Pre-built Module
1. Download the latest `dominations_zygisk_hook.zip` from releases
2. Install via Magisk Manager or APatch Manager
3. Reboot your device
4. Launch Dominations to activate hooks

### Method 2: Build from Source
1. Clone this repository
2. Install Android NDK
3. Set `ANDROID_NDK_ROOT` environment variable
4. Run the build script:
   ```bash
   chmod +x build.sh
   ./build.sh
   ```
5. Install the generated ZIP file

## 🎮 Using the Mod Menu

### Accessing the Menu
1. **Launch Dominations** after installing the module
2. **Touch the top-left corner** of the screen to open the mod menu
3. **Navigate categories** to find desired features
4. **Toggle switches** to enable/disable features
5. **Adjust sliders** for multiplier values
6. **Touch outside** the menu to close it

### Menu Categories

#### 💰 Resource Hacks
- **Unlimited Resources**: Infinite gold, gems, and materials
- **Resource Multiplier**: 1x to 100x resource gain multiplier
- **Auto Collect**: Automatically collect resources from buildings

#### 🏗️ Building Hacks
- **Instant Build**: Complete construction immediately
- **Speed Hack**: Accelerate building and research times

#### ⚔️ Battle Hacks
- **No Troop Cost**: Train troops for free
- **God Mode**: Your troops take no damage in battle

#### 🚀 Speed Hacks
- **Speed Hack**: Enable game speed acceleration
- **Speed Multiplier**: 1x to 10x game speed control

#### 🛡️ Safety
- **Anti-Ban Protection**: Enable safety measures (recommended)

#### ⚙️ Settings
- **Save Settings**: Persist current configuration
- **Load Settings**: Restore saved configuration
- **Reset Settings**: Return to default values

## Configuration

The module automatically detects and hooks into the Dominations game. No additional configuration is required for basic functionality.

### Advanced Configuration
- Logs are written to `/data/local/tmp/dominations_hook.log`
- Enable verbose logging by uncommenting lines in `service.sh`
- Modify hook implementations in `jni/hooks.cpp`

## Architecture

```
dominations_zygisk_hook/
├── module.prop              # Magisk module metadata
├── apatch.json             # APatch compatibility
├── META-INF/               # Installation scripts
├── jni/                    # Native code
│   ├── main.cpp           # Zygisk module entry point
│   ├── hook.h/.cpp        # Hook management system
│   ├── hooks.cpp          # Game-specific hooks
│   ├── logging.h/.cpp     # Logging utilities
│   ├── zygisk.hpp         # Zygisk API header
│   ├── Android.mk         # NDK build config
│   └── CMakeLists.txt     # CMake build config
├── lib/                   # Compiled native libraries
├── service.sh             # Boot service script
└── build.sh              # Build automation script
```

## Hook Categories

### Game Logic Hooks
- **Resource Management**: Monitor gold, gems, and other resources
- **Building System**: Track construction and upgrades
- **Battle System**: Analyze combat mechanics
- **Research Tree**: Study technology progression

### Network Hooks
- **API Requests**: Log all server communications
- **Authentication**: Monitor login and session management
- **Data Synchronization**: Track game state updates
- **Purchase Validation**: Analyze in-app purchase flow

### Anti-Cheat Analysis
- **Memory Protection**: Study memory integrity checks
- **Code Obfuscation**: Analyze anti-reverse engineering measures
- **Runtime Detection**: Monitor anti-debugging techniques
- **Validation Systems**: Study server-side validation

## Development

### Adding New Hooks
1. Define hook structure in `jni/hook.h`
2. Implement hook logic in `jni/hooks.cpp`
3. Register hook in appropriate category function
4. Rebuild and test

### Debugging
- Check logs: `adb logcat | grep DominationsHook`
- Module logs: `cat /data/local/tmp/dominations_hook.log`
- Zygisk status: Check Magisk/APatch logs

### Reverse Engineering Tips
1. Use APK analysis tools (jadx, apktool)
2. Identify target methods and classes
3. Update hook definitions with real class/method names
4. Test hooks incrementally

## Safety and Legal Considerations

⚠️ **Important Disclaimers**:
- This module is for **educational and research purposes only**
- Modifying games may violate Terms of Service
- Use at your own risk - may result in account bans
- Respect intellectual property rights
- Only use on games you own or have permission to modify

## Troubleshooting

### Module Not Loading
- Ensure Zygisk is enabled in Magisk/APatch
- Check module is installed correctly
- Verify target app package name
- Review installation logs

### Hooks Not Working
- Confirm target app is running
- Check class/method names are correct
- Verify hook signatures match target methods
- Enable verbose logging for debugging

### Build Issues
- Ensure Android NDK is properly installed
- Check NDK path environment variable
- Verify all source files are present
- Review build script permissions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Magisk](https://github.com/topjohnwu/Magisk) - The Magic Mask for Android
- [APatch](https://github.com/bmax121/APatch) - Android Kernel Patch
- [Zygisk](https://github.com/topjohnwu/Magisk/blob/master/docs/guides.md#zygisk) - Zygote-based injection framework
- Android reverse engineering community

## Disclaimer

This software is provided "as is" without warranty of any kind. The authors are not responsible for any damage or legal issues that may arise from using this software.
