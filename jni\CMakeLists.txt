cmake_minimum_required(VERSION 3.18.1)

project("dominations_zygisk")

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fvisibility=hidden -fvisibility-inlines-hidden")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -ffunction-sections -fdata-sections")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2 -DNDEBUG")

# Linker flags
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--gc-sections,--strip-all")

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# Source files
set(SOURCES
    main.cpp
    hook.cpp
    hooks.cpp
    logging.cpp
    menu.cpp
)

# Create shared library
add_library(dominations_zygisk SHARED ${SOURCES})

# Link libraries
target_link_libraries(dominations_zygisk
    log
    dl
)

# Set output name and properties
set_target_properties(dominations_zygisk PROPERTIES
    OUTPUT_NAME "dominations_zygisk"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/../lib/${ANDROID_ABI}"
)
