#include "hook.h"
#include "logging.h"
#include <dlfcn.h>
#include <sys/mman.h>
#include <unistd.h>
#include <fstream>
#include <sstream>
#include <algorithm>

namespace DominationsHook {

// Static instance
HookManager* HookManager::instance = nullptr;

HookManager* HookManager::getInstance() {
    if (!instance) {
        instance = new HookManager();
    }
    return instance;
}

bool HookManager::hookJNIMethod(const JNIMethodHook& hook) {
    LOG_ENTRY();
    
    try {
        // This is a placeholder for JNI method hooking
        // In a real implementation, you would use libraries like:
        // - <PERSON>bby (https://github.com/jmpews/Dobby)
        // - Substrate/MSHookFunction
        // - Frida-gum
        
        LOGI("Hooking JNI method: %s.%s%s", hook.class_name, hook.method_name, hook.method_signature);
        
        // Store the hook for later management
        jni_hooks.push_back(hook);
        
        LOGI("JNI method hook applied successfully");
        return true;
        
    } catch (const std::exception& e) {
        LOGE("Failed to hook JNI method: %s", e.what());
        return false;
    }
}

bool HookManager::unhookJNIMethod(const JNIMethodHook& hook) {
    LOG_ENTRY();
    
    // Remove from our tracking
    auto it = std::find_if(jni_hooks.begin(), jni_hooks.end(),
        [&hook](const JNIMethodHook& h) {
            return strcmp(h.class_name, hook.class_name) == 0 &&
                   strcmp(h.method_name, hook.method_name) == 0 &&
                   strcmp(h.method_signature, hook.method_signature) == 0;
        });
    
    if (it != jni_hooks.end()) {
        jni_hooks.erase(it);
        LOGI("JNI method unhook completed");
        return true;
    }
    
    LOGW("JNI method hook not found for unhooking");
    return false;
}

bool HookManager::applyMemoryPatch(void* address, const std::vector<uint8_t>& patch_bytes) {
    LOG_ENTRY();
    
    if (!address || patch_bytes.empty()) {
        LOGE("Invalid parameters for memory patch");
        return false;
    }
    
    try {
        // Get page size and align address
        size_t page_size = getpagesize();
        void* page_start = reinterpret_cast<void*>(
            reinterpret_cast<uintptr_t>(address) & ~(page_size - 1));
        
        // Make memory writable
        if (mprotect(page_start, page_size, PROT_READ | PROT_WRITE | PROT_EXEC) != 0) {
            LOGE("Failed to make memory writable");
            return false;
        }
        
        // Store original bytes
        MemoryPatch patch;
        patch.address = address;
        patch.original_bytes.resize(patch_bytes.size());
        memcpy(patch.original_bytes.data(), address, patch_bytes.size());
        patch.patch_bytes = patch_bytes;
        patch.is_applied = false;
        
        // Apply patch
        memcpy(address, patch_bytes.data(), patch_bytes.size());
        patch.is_applied = true;
        
        // Restore memory protection
        if (mprotect(page_start, page_size, PROT_READ | PROT_EXEC) != 0) {
            LOGW("Failed to restore memory protection");
        }
        
        memory_patches.push_back(patch);
        
        LOGI("Memory patch applied at %p, size: %zu", address, patch_bytes.size());
        log_hex_dump("Original", patch.original_bytes.data(), patch.original_bytes.size());
        log_hex_dump("Patched", patch_bytes.data(), patch_bytes.size());
        
        return true;
        
    } catch (const std::exception& e) {
        LOGE("Failed to apply memory patch: %s", e.what());
        return false;
    }
}

bool HookManager::revertMemoryPatch(void* address) {
    LOG_ENTRY();
    
    auto it = std::find_if(memory_patches.begin(), memory_patches.end(),
        [address](const MemoryPatch& patch) {
            return patch.address == address && patch.is_applied;
        });
    
    if (it == memory_patches.end()) {
        LOGW("Memory patch not found for reverting");
        return false;
    }
    
    try {
        // Get page size and align address
        size_t page_size = getpagesize();
        void* page_start = reinterpret_cast<void*>(
            reinterpret_cast<uintptr_t>(address) & ~(page_size - 1));
        
        // Make memory writable
        if (mprotect(page_start, page_size, PROT_READ | PROT_WRITE | PROT_EXEC) != 0) {
            LOGE("Failed to make memory writable for revert");
            return false;
        }
        
        // Restore original bytes
        memcpy(address, it->original_bytes.data(), it->original_bytes.size());
        it->is_applied = false;
        
        // Restore memory protection
        if (mprotect(page_start, page_size, PROT_READ | PROT_EXEC) != 0) {
            LOGW("Failed to restore memory protection after revert");
        }
        
        LOGI("Memory patch reverted at %p", address);
        return true;
        
    } catch (const std::exception& e) {
        LOGE("Failed to revert memory patch: %s", e.what());
        return false;
    }
}

void* HookManager::findSymbol(const char* lib_name, const char* symbol_name) {
    LOG_ENTRY();
    
    void* handle = dlopen(lib_name, RTLD_LAZY);
    if (!handle) {
        LOGE("Failed to open library %s: %s", lib_name, dlerror());
        return nullptr;
    }
    
    void* symbol = dlsym(handle, symbol_name);
    if (!symbol) {
        LOGE("Failed to find symbol %s in %s: %s", symbol_name, lib_name, dlerror());
        dlclose(handle);
        return nullptr;
    }
    
    LOGI("Found symbol %s at %p in %s", symbol_name, symbol, lib_name);
    return symbol;
}

void* HookManager::findPattern(void* start, size_t size, const std::vector<uint8_t>& pattern) {
    LOG_ENTRY();
    
    if (!start || size == 0 || pattern.empty()) {
        return nullptr;
    }
    
    const uint8_t* data = static_cast<const uint8_t*>(start);
    
    for (size_t i = 0; i <= size - pattern.size(); ++i) {
        bool found = true;
        for (size_t j = 0; j < pattern.size(); ++j) {
            if (data[i + j] != pattern[j]) {
                found = false;
                break;
            }
        }
        
        if (found) {
            void* result = static_cast<void*>(const_cast<uint8_t*>(&data[i]));
            LOGI("Pattern found at %p", result);
            return result;
        }
    }
    
    LOGD("Pattern not found in memory range");
    return nullptr;
}

void HookManager::cleanup() {
    LOG_ENTRY();
    
    // Revert all memory patches
    for (auto& patch : memory_patches) {
        if (patch.is_applied) {
            revertMemoryPatch(patch.address);
        }
    }
    memory_patches.clear();
    
    // Clear JNI hooks (actual unhooking would be done by the hooking library)
    jni_hooks.clear();
    
    LOGI("Hook manager cleanup completed");
}

// Utility functions
bool isTargetProcess(const char* package_name) {
    return package_name && strcmp(package_name, "com.nexonm.dominations.adk") == 0;
}

std::string getProcessName() {
    std::ifstream cmdline("/proc/self/cmdline");
    std::string name;
    if (cmdline.is_open()) {
        std::getline(cmdline, name, '\0');
        cmdline.close();
    }
    return name;
}

bool hasRootAccess() {
    return getuid() == 0 || geteuid() == 0;
}

void protectMemory(void* addr, size_t size, int prot) {
    size_t page_size = getpagesize();
    void* page_start = reinterpret_cast<void*>(
        reinterpret_cast<uintptr_t>(addr) & ~(page_size - 1));
    
    if (mprotect(page_start, size + (reinterpret_cast<uintptr_t>(addr) - reinterpret_cast<uintptr_t>(page_start)), prot) != 0) {
        LOGE("Failed to change memory protection");
    }
}

} // namespace DominationsHook
